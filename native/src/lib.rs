use color_eyre::eyre::{ContextCompat, OptionExt};
use common::utils::stats::calculate_sum;
use jni::objects::{JClass, JObject, JString, JValue};
use jni::sys::{jboolean, jdouble, jdoubleArray, jobject, jstring};
use jni::JNIEnv;
use jni_bridge::jni_bridge::JavaClasses;
use jni_bridge::{create_big_decimal, get_double_array, handle_unwinded_scope};

use color_eyre::Result;
use std::error::Error;

#[no_mangle]
pub extern "system" fn Java_com_guwave_onedata_dataware_dw_testItem_spark_jni_Native_00024_sum(
    mut env: JNIEnv,
    _class: JClass,
    values: jdoubleArray,
) -> jobject {
    if !JavaClasses::inited() {
        JavaClasses::init(&env);
    }
    handle_unwinded_scope(|| -> Result<jobject, Box<dyn Error + Send + Sync>> {
        let arr = get_double_array(&mut env, values).ok_or_eyre("Failed to get double array")?;
        let value = calculate_sum(&arr).ok_or_eyre("Failed to calculate sum")?;
        Ok(create_big_decimal(&mut env, value))
    })
}


#[no_mangle]
pub extern "system" fn Java_com_guwave_onedata_dataware_dw_testItem_spark_jni_Native_00024_cp_task(
    mut env: JNIEnv,
    _class: JClass,
    values: jdoubleArray,
) -> jobject {
    if !JavaClasses::inited() {
        JavaClasses::init(&env);
    }
    handle_unwinded_scope(|| -> Result<jobject, Box<dyn Error + Send + Sync>> {
        
    })
}